# 绘本功能API接口文档

## 概述

本文档详细描述了 DeepCreate 项目中AI绘本创作功能的所有 API 接口，包括绘本作品管理、页面管理、角色管理、图像生成等功能。

## 接口分类

### 1. 绘本作品管理

#### 1.1 创建绘本

**接口地址**: `POST /storybook/works`

**功能描述**: 创建新的绘本作品

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface CreateStorybookRequest {
  title: string;                     // 绘本标题
  description?: string;              // 绘本描述
  theme?: string;                    // 主题
  targetAge?: string;                // 目标年龄
  folderId?: number;                 // 文件夹ID
  coverImage?: string;               // 封面图片URL
  tags?: string[];                   // 标签
}
```

**请求示例**:
```json
{
  "title": "小兔子的冒险",
  "description": "一个关于勇敢小兔子的故事",
  "theme": "冒险",
  "targetAge": "3-6岁",
  "tags": ["冒险", "友谊", "勇敢"]
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "创建成功",
  "data": {
    "id": 123,
    "title": "小兔子的冒险",
    "description": "一个关于勇敢小兔子的故事",
    "theme": "冒险",
    "targetAge": "3-6岁",
    "status": 0,
    "userId": 456,
    "folderId": null,
    "coverImage": null,
    "pageCount": 0,
    "createdAt": "2024-01-20T10:30:00.000Z",
    "updatedAt": "2024-01-20T10:30:00.000Z"
  }
}
```

#### 1.2 获取作品列表

**接口地址**: `GET /storybook/works`

**功能描述**: 获取用户的绘本作品列表

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface QueryStorybookRequest {
  page?: number;                     // 页码，默认1
  size?: number;                     // 每页数量，默认20
  status?: number;                   // 状态筛选：0-草稿，1-已发布，2-审核中，3-已拒绝
  folderId?: number;                 // 文件夹ID筛选
  keyword?: string;                  // 搜索关键词
  sortBy?: string;                   // 排序字段：createdAt, updatedAt, title
  sortOrder?: 'ASC' | 'DESC';        // 排序方向
}
```

**请求示例**:
```
GET /storybook/works?page=1&size=10&status=0&keyword=小兔子
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "id": 123,
        "title": "小兔子的冒险",
        "description": "一个关于勇敢小兔子的故事",
        "theme": "冒险",
        "targetAge": "3-6岁",
        "status": 0,
        "statusText": "草稿",
        "coverImage": "https://example.com/cover.jpg",
        "pageCount": 5,
        "folderId": null,
        "folderName": null,
        "createdAt": "2024-01-20T10:30:00.000Z",
        "updatedAt": "2024-01-20T11:00:00.000Z"
      }
    ],
    "count": 25,
    "page": 1,
    "size": 10
  }
}
```

#### 1.3 获取绘本详情

**接口地址**: `GET /storybook/works/{id}`

**功能描述**: 获取指定绘本的详细信息

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
- `id`: 绘本ID

**响应格式**:
```json
{
  "success": true,
  "data": {
    "id": 123,
    "title": "小兔子的冒险",
    "description": "一个关于勇敢小兔子的故事",
    "theme": "冒险",
    "targetAge": "3-6岁",
    "status": 0,
    "coverImage": "https://example.com/cover.jpg",
    "pageCount": 5,
    "userId": 456,
    "folderId": null,
    "tags": ["冒险", "友谊", "勇敢"],
    "createdAt": "2024-01-20T10:30:00.000Z",
    "updatedAt": "2024-01-20T11:00:00.000Z",
    "pages": [
      {
        "id": 1,
        "pageNumber": 1,
        "content": "从前有一只小兔子...",
        "imageUrl": "https://example.com/page1.jpg",
        "imagePrompt": "一只可爱的小兔子在森林里"
      }
    ],
    "characters": [
      {
        "id": 1,
        "name": "小兔子",
        "description": "勇敢的主角",
        "appearance": "白色毛发，红色眼睛",
        "personality": "勇敢、善良、聪明"
      }
    ]
  }
}
```

#### 1.4 更新绘本

**接口地址**: `PUT /storybook/works/{id}`

**功能描述**: 更新绘本信息

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface UpdateStorybookRequest {
  title?: string;                    // 绘本标题
  description?: string;              // 绘本描述
  theme?: string;                    // 主题
  targetAge?: string;                // 目标年龄
  coverImage?: string;               // 封面图片URL
  tags?: string[];                   // 标签
  status?: number;                   // 状态
}
```

#### 1.5 删除绘本

**接口地址**: `DELETE /storybook/works/{id}`

**功能描述**: 删除绘本作品

**请求头**:
```
Authorization: Bearer {token}
```

### 2. 绘本页面管理

#### 2.1 创建页面

**接口地址**: `POST /storybook/page`

**功能描述**: 为绘本创建新页面

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface CreatePageRequest {
  storybookId: number;               // 绘本ID
  pageNumber: number;                // 页码
  content: string;                   // 页面文本内容
  imagePrompt?: string;              // 图像生成提示词
  layout?: string;                   // 页面布局：image-top, image-left, image-right
}
```

**请求示例**:
```json
{
  "storybookId": 123,
  "pageNumber": 1,
  "content": "从前有一只小兔子，住在美丽的森林里。",
  "imagePrompt": "一只可爱的白色小兔子在绿色森林里，阳光透过树叶洒下",
  "layout": "image-top"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "页面创建成功",
  "data": {
    "id": 1,
    "storybookId": 123,
    "pageNumber": 1,
    "content": "从前有一只小兔子，住在美丽的森林里。",
    "imagePrompt": "一只可爱的白色小兔子在绿色森林里，阳光透过树叶洒下",
    "imageUrl": null,
    "layout": "image-top",
    "createdAt": "2024-01-20T10:30:00.000Z"
  }
}
```

#### 2.2 更新页面

**接口地址**: `PUT /storybook/page/{id}`

**功能描述**: 更新绘本页面

**请求参数**:
```typescript
interface UpdatePageRequest {
  content?: string;                  // 页面文本内容
  imagePrompt?: string;              // 图像生成提示词
  imageUrl?: string;                 // 图片URL
  layout?: string;                   // 页面布局
}
```

#### 2.3 删除页面

**接口地址**: `DELETE /storybook/page/{id}`

**功能描述**: 删除绘本页面

#### 2.4 获取绘本所有页面

**接口地址**: `GET /storybook/{id}/pages`

**功能描述**: 获取指定绘本的所有页面

**路径参数**:
- `id`: 绘本ID

**响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "pageNumber": 1,
      "content": "从前有一只小兔子，住在美丽的森林里。",
      "imageUrl": "https://example.com/page1.jpg",
      "imagePrompt": "一只可爱的白色小兔子在绿色森林里",
      "layout": "image-top"
    },
    {
      "id": 2,
      "pageNumber": 2,
      "content": "小兔子决定去探险。",
      "imageUrl": "https://example.com/page2.jpg",
      "imagePrompt": "小兔子背着小包准备出发探险",
      "layout": "image-top"
    }
  ]
}
```

### 3. 角色管理

#### 3.1 创建角色

**接口地址**: `POST /storybook/character`

**功能描述**: 为绘本创建角色

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface CreateCharacterRequest {
  storybookId: number;               // 绘本ID
  name: string;                      // 角色名称
  description?: string;              // 角色描述
  appearance: string;                // 外貌特征
  personality?: string;              // 性格特点
  role?: string;                     // 角色定位：主角、配角、反派等
  avatar?: string;                   // 角色头像URL
}
```

**请求示例**:
```json
{
  "storybookId": 123,
  "name": "小兔子",
  "description": "故事的主角，一只勇敢的小兔子",
  "appearance": "白色毛发，红色眼睛，长长的耳朵",
  "personality": "勇敢、善良、聪明、好奇",
  "role": "主角"
}
```

#### 3.2 获取角色模板

**接口地址**: `GET /storybook/character/templates`

**功能描述**: 获取预设的角色模板

**响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "勇敢的小兔子",
      "category": "动物",
      "appearance": "白色毛发，红色眼睛，长长的耳朵",
      "personality": "勇敢、善良、聪明",
      "traits": ["勇敢", "善良", "聪明", "好奇", "活泼"],
      "avatar": "https://example.com/rabbit-template.jpg"
    }
  ]
}
```

#### 3.3 获取我的角色库

**接口地址**: `GET /storybook/character/mine`

**功能描述**: 获取用户创建的所有角色

**请求头**:
```
Authorization: Bearer {token}
```

### 4. 图像生成

#### 4.1 生成图像

**接口地址**: `POST /storybook/image`

**功能描述**: 根据提示词生成图像

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface CreateImageRequest {
  prompt: string;                    // 图像生成提示词
  storybookId?: number;              // 关联的绘本ID
  pageId?: number;                   // 关联的页面ID
  style?: string;                    // 图像风格
  size?: string;                     // 图像尺寸
  quality?: string;                  // 图像质量
}
```

**请求示例**:
```json
{
  "prompt": "一只可爱的白色小兔子在绿色森林里，阳光透过树叶洒下，儿童绘本风格",
  "storybookId": 123,
  "pageId": 1,
  "style": "cartoon",
  "size": "1024x1024",
  "quality": "standard"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "图像生成成功",
  "data": {
    "id": 1,
    "imageUrl": "https://example.com/generated-image.jpg",
    "prompt": "一只可爱的白色小兔子在绿色森林里...",
    "style": "cartoon",
    "size": "1024x1024",
    "storybookId": 123,
    "pageId": 1,
    "status": "completed",
    "createdAt": "2024-01-20T10:30:00.000Z"
  }
}
```

#### 4.2 获取图像列表

**接口地址**: `GET /storybook/image`

**功能描述**: 获取用户生成的图像列表

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface QueryImageRequest {
  page?: number;                     // 页码
  size?: number;                     // 每页数量
  storybookId?: number;              // 绘本ID筛选
  status?: string;                   // 状态筛选
}
```

### 5. 文件夹管理

#### 5.1 创建文件夹

**接口地址**: `POST /storybook/works/folder`

**功能描述**: 创建作品文件夹

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface CreateFolderRequest {
  name: string;                      // 文件夹名称
  description?: string;              // 文件夹描述
  parentId?: number;                 // 父文件夹ID
}
```

#### 5.2 获取文件夹列表

**接口地址**: `GET /storybook/works/folders`

**功能描述**: 获取用户的文件夹列表

**请求头**:
```
Authorization: Bearer {token}
```

#### 5.3 移动作品到文件夹

**接口地址**: `POST /storybook/works/move`

**功能描述**: 将作品移动到指定文件夹

**请求参数**:
```typescript
interface MoveToFolderRequest {
  storybookIds: number[];            // 绘本ID数组
  folderId: number | null;           // 目标文件夹ID，null表示移到根目录
}
```

### 6. 导出功能

#### 6.1 导出为PDF

**接口地址**: `POST /storybook/works/{id}/export/pdf`

**功能描述**: 将绘本导出为PDF文件

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface ExportPDFRequest {
  includeImages?: boolean;           // 是否包含图片，默认true
  pageSize?: string;                 // 页面尺寸：A4, A5等
  orientation?: string;              // 页面方向：portrait, landscape
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "导出成功",
  "data": {
    "url": "https://example.com/exports/storybook_123.pdf",
    "filename": "小兔子的冒险.pdf",
    "size": 2048576
  }
}
```

#### 6.2 导出为图片集

**接口地址**: `POST /storybook/works/{id}/export/images`

**功能描述**: 将绘本导出为图片集ZIP文件

**请求参数**:
```typescript
interface ExportImagesRequest {
  quality?: 'high' | 'medium' | 'low'; // 图片质量
  format?: 'png' | 'jpg';            // 图片格式
  range?: 'all' | 'custom';          // 导出范围
  startPage?: number;                // 起始页码（range为custom时）
  endPage?: number;                  // 结束页码（range为custom时）
}
```

### 7. 配置相关

#### 7.1 获取绘本配置

**接口地址**: `GET /storybook/config`

**功能描述**: 获取绘本功能配置

**响应格式**:
```json
{
  "success": true,
  "data": {
    "maxPagesPerStorybook": 20,
    "maxCharactersPerStorybook": 10,
    "supportedImageFormats": ["jpg", "png", "webp"],
    "maxImageSize": 5242880,
    "defaultImageStyle": "cartoon",
    "availableStyles": ["cartoon", "realistic", "watercolor", "sketch"]
  }
}
```

#### 7.2 获取图像生成配置

**接口地址**: `GET /storybook/image-config/1`

**功能描述**: 获取图像生成相关配置

**响应格式**:
```json
{
  "success": true,
  "data": {
    "model": "dall-e-3",
    "size": "1024x1024",
    "quality": "standard",
    "style": "vivid",
    "fallbackService": "https://image.pollinations.ai/prompt/%s",
    "enabled": true
  }
}
```

#### 7.3 获取小狐狸助手配置

**接口地址**: `GET /storybook/fox-assistant-config`

**功能描述**: 获取AI绘本创作助手配置

**响应格式**:
```json
{
  "success": true,
  "data": {
    "model": "gpt-3.5-turbo",
    "systemPrompt": "你是一个专业的儿童绘本创作助手...",
    "maxTokens": 2000,
    "temperature": 0.7,
    "enabled": true
  }
}
```

## 前端实现示例

### 1. API客户端封装

```typescript
import { get, post, put, del } from '@/utils/request';

export const storybookAPI = {
  // 作品管理
  createStorybook: (data: CreateStorybookRequest) => 
    post({ url: '/storybook/works', data }),
  
  getStorybookList: (params: QueryStorybookRequest) => 
    get({ url: '/storybook/works', params }),
  
  getStorybookDetail: (id: number) => 
    get({ url: `/storybook/works/${id}` }),
  
  updateStorybook: (id: number, data: UpdateStorybookRequest) => 
    put({ url: `/storybook/works/${id}`, data }),
  
  deleteStorybook: (id: number) => 
    del({ url: `/storybook/works/${id}` }),

  // 页面管理
  createPage: (data: CreatePageRequest) => 
    post({ url: '/storybook/page', data }),
  
  updatePage: (id: number, data: UpdatePageRequest) => 
    put({ url: `/storybook/page/${id}`, data }),
  
  deletePage: (id: number) => 
    del({ url: `/storybook/page/${id}` }),
  
  getStorybookPages: (storybookId: number) => 
    get({ url: `/storybook/${storybookId}/pages` }),

  // 角色管理
  createCharacter: (data: CreateCharacterRequest) => 
    post({ url: '/storybook/character', data }),
  
  getCharacterTemplates: () => 
    get({ url: '/storybook/character/templates' }),
  
  getUserCharacters: () => 
    get({ url: '/storybook/character/mine' }),

  // 图像生成
  generateImage: (data: CreateImageRequest) => 
    post({ url: '/storybook/image', data }),
  
  getImageList: (params: QueryImageRequest) => 
    get({ url: '/storybook/image', params }),

  // 文件夹管理
  createFolder: (data: CreateFolderRequest) => 
    post({ url: '/storybook/works/folder', data }),
  
  getFolders: () => 
    get({ url: '/storybook/works/folders' }),
  
  moveToFolder: (data: MoveToFolderRequest) => 
    post({ url: '/storybook/works/move', data }),

  // 导出功能
  exportToPDF: (id: number, data: ExportPDFRequest) => 
    post({ url: `/storybook/works/${id}/export/pdf`, data }),
  
  exportToImages: (id: number, data: ExportImagesRequest) => 
    post({ url: `/storybook/works/${id}/export/images`, data }),

  // 配置
  getConfig: () => 
    get({ url: '/storybook/config' }),
  
  getImageConfig: () => 
    get({ url: '/storybook/image-config/1' }),
  
  getFoxAssistantConfig: () => 
    get({ url: '/storybook/fox-assistant-config' })
};
```

### 2. 状态管理

```typescript
export const useStorybookStore = defineStore('storybook', {
  state: () => ({
    currentStorybook: null as any,
    storybookList: [] as any[],
    folders: [] as any[],
    loading: false,
    config: null as any
  }),

  actions: {
    async createStorybook(data: CreateStorybookRequest) {
      this.loading = true;
      try {
        const response = await storybookAPI.createStorybook(data);
        this.storybookList.unshift(response.data);
        return response.data;
      } finally {
        this.loading = false;
      }
    },

    async loadStorybookList(params: QueryStorybookRequest = {}) {
      this.loading = true;
      try {
        const response = await storybookAPI.getStorybookList(params);
        this.storybookList = response.data.rows;
        return response.data;
      } finally {
        this.loading = false;
      }
    },

    async loadStorybookDetail(id: number) {
      this.loading = true;
      try {
        const response = await storybookAPI.getStorybookDetail(id);
        this.currentStorybook = response.data;
        return response.data;
      } finally {
        this.loading = false;
      }
    }
  }
});
```
