<script setup lang="ts">
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { useAppStore, useAuthStore, useChatStore } from '@/store';
import { NLayout, NWatermark, useMessage } from 'naive-ui';
import {
  computed,
  nextTick,
  onMounted,
  onUnmounted,
  provide,
  ref,
  watch,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ChatBase from './chatBase.vue';
import CodePreview from './components/CodePreview/index.vue';
import QuickStartSidebar from './components/QuickStartSidebar.vue';
const ms = useMessage();
const route = useRoute();
const router = useRouter();
const appStore = useAppStore();
const chatStore = useChatStore();
const authStore = useAuthStore();
const { isMobile } = useBasicLayout();
const isLogin = computed(() => authStore.isLogin);
const siderCollapsed = computed(() => appStore.siderCollapsed);

// 双栏布局宽度计算：聊天区域 + 代码预览区域
const chatAreaClass = computed(() => {
  if (isMobile.value) return 'w-full';

  // 桌面端：根据代码预览的显示状态和内容调整聊天区域宽度
  if (previewVisible.value && currentCode.value) {
    // 有代码且显示代码预览：聊天区域占1/2
    return 'w-1/2';
  }
  // 没有代码或不显示代码预览：聊天区域占全部
  return 'w-full';
});

// 聊天内容容器的样式类
const chatContentClass = computed(() => {
  if (isMobile.value) return 'w-full';

  // 桌面端：根据预览状态调整内容容器宽度和居中
  if (previewVisible.value && currentCode.value) {
    // 双栏模式：内容占满容器
    return 'w-full';
  }
  // 单栏模式：限制最大宽度并居中
  return 'w-full max-w-6xl mx-auto';
});

const codePreviewAreaClass = computed(() => {
  if (isMobile.value) {
    return (previewVisible.value && currentCode.value) ? 'w-full absolute inset-0 z-20' : 'hidden';
  }

  // 桌面端：只有在有代码且预览可见时才显示
  return (previewVisible.value && currentCode.value) ? 'w-1/2' : 'hidden';
});

// 快速开始侧边栏显示状态
const quickStartAreaClass = computed(() => {
  if (isMobile.value) {
    // 移动端不显示快速开始侧边栏
    return 'hidden';
  }

  // 桌面端：当没有代码预览时显示快速开始侧边栏
  return (!previewVisible.value || !currentCode.value) ? 'w-1/2' : 'hidden';
});

// 当前代码内容
const currentCode = ref('');

// 代码预览区域显示状态 - 智能初始化
const previewVisible = ref(false); // 初始状态不显示，等有代码时再显示

// 移除应用中心相关逻辑

// 移除切换视图逻辑，只保留代码预览
const appId = computed(() => route.query.appId as string);
const startX = ref(0);
const endX = ref(0);

const showWatermark = computed(
  () => Number(authStore.globalConfig?.showWatermark) === 1
);
const userId = ref(authStore.userInfo.id ?? '');
const isModelInherited = computed(
  () => Number(authStore.globalConfig?.isModelInherited) === 1
);
const isStreamIn = computed(() => {
  return chatStore.isStreamIn !== undefined ? chatStore.isStreamIn : false;
});

watch(isLogin, async (newVal, oldVal) => {
  if (newVal && !oldVal) {
    await chatStore.queryMyGroup();
    // router.replace('/chat');
    window.location.href = '/chat';
  }
});

const getMobileClass = computed(() => {
  if (isMobile.value) return ['rounded-none', 'shadow-none'];
  return ['rounded-lg', 'shadow-lg', 'dark:border-gray-900'];
});

const getContainerClass = computed(() => {
  return ['h-full', 'rounded-lg', 'overflow-hidden'];
});

// 移动端抽屉配置（已移除，聊天历史功能已移到顶部标签页）

// 监听窗口大小变化，触发侧边栏高度更新
onMounted(() => {
  window.addEventListener('resize', triggerSidebarHeightUpdate);

  // 移除应用中心事件监听

  // 监听显示预览区域的事件
  window.addEventListener('show-preview', handleShowPreview);
});

onUnmounted(() => {
  window.removeEventListener('resize', triggerSidebarHeightUpdate);
  // 移除应用中心事件监听
  window.removeEventListener('show-preview', handleShowPreview);
});

// 处理显示预览区域事件
function handleShowPreview() {
  console.log('收到显示预览区域事件');
  // 确保预览区域可见
  previewVisible.value = true;
}

// 触发侧边栏高度更新
function triggerSidebarHeightUpdate() {
  // 创建一个自定义事件，通知侧边栏更新高度
  window.dispatchEvent(new CustomEvent('update-sidebar-height'));
}

/* 新增一个对话 */
async function createNewChatGroup(appId?: number) {
  if (isStreamIn.value) {
    ms.info('AI回复中，请稍后再试');
    return;
  }

  chatStore.setStreamIn(false);
  try {
    // addLoading.value = true;
    // 检查 activeConfig 是否存在
    if (appId && appId > 0) {
      await chatStore.addNewChatGroup(appId);
    } else {
      const { modelInfo } = chatStore.activeConfig;
      if (
        modelInfo &&
        isModelInherited.value &&
        chatStore.activeGroupAppId === 0
      ) {
        const config = {
          modelInfo,
        };
        await chatStore.addNewChatGroup(0, config);
      } else {
        await chatStore.addNewChatGroup();
      }
    }

    // chatStore.queryMyGroup();
    // addLoading.value = false;
    chatStore.setUsingPlugin(null);

    if (isMobile.value) {
      appStore.setSiderCollapsed(true);
    }
  } catch (error) {
    // addLoading.value = false;
  }
}

function handleTouchStart(event: any) {
  startX.value = event.touches[0].clientX;
}

function handleTouchEnd(event: any) {
  endX.value = event.changedTouches[0].clientX;
  if (endX.value - startX.value > 100) {
    // 判断向右滑动的最小距离（可以根据需要调整）
    if (isMobile.value) {
      appStore.setSiderCollapsed(false);
    }
  }
}

onMounted(() => {
  window.addEventListener('touchstart', handleTouchStart);
  window.addEventListener('touchend', handleTouchEnd);
});

onUnmounted(() => {
  window.removeEventListener('touchstart', handleTouchStart);
  window.removeEventListener('touchend', handleTouchEnd);
});

// onMounted(async () => {
//   if (dataSources.value.length === 0) {
//     await nextTick();
//     createNewChatGroup();
//   }
// });

provide('createNewChatGroup', createNewChatGroup);

// 提供当前代码内容给子组件
provide('updateCurrentCode', (code: string) => {
  currentCode.value = code;
  // 智能显示预览区域：当有代码时自动显示
  if (code && code.trim()) {
    previewVisible.value = true;
  }
});

// 处理代码更新
function handleCodeUpdate(newCode: string) {
  currentCode.value = newCode;
  updateHtmlEditors(newCode);
  ms.success('HTML已更新');
}

// 处理元素编辑
function handleElementEdited(data: any) {
  console.log('元素已编辑:', data);
  ms.success(`已编辑元素: ${data.element}`);
}

// 处理快速开始提示
function handleUsePrompt(prompt: string) {
  // 创建一个自定义事件，通知聊天组件使用提示
  const event = new CustomEvent('use-prompt', {
    detail: { prompt }
  });
  window.dispatchEvent(event);
}

// 更新所有HTML编辑器
function updateHtmlEditors(newCode: string) {
  const updateEvent = new CustomEvent('update-html-editor', {
    detail: { code: newCode }
  });
  window.dispatchEvent(updateEvent);
}

watch(
  appId,
  async (newVal, oldVal) => {
    if (newVal) {
      const id = +newVal; // 转换appId为数字类型
      router.replace('/chat');
      // await router.replace({ path: '/chat', query: {} }); // 清除当前查询参数
      await nextTick(); // 等待导航完成
      createNewChatGroup(id); // 调用创建新对话组的方法
    }
  },
  { immediate: true } // 立即执行，处理组件加载时的逻辑
);
</script>

<template>
  <NWatermark
    v-if="showWatermark && isLogin"
    :content="'#' + userId"
    cross
    fullscreen
    :font-size="16"
    :line-height="16"
    :width="384"
    :height="384"
    :x-offset="12"
    :y-offset="60"
    :rotate="-15"
  />
  <!-- 美化的主背景容器 - 教育主题设计 -->
  <div class="h-screen max-h-screen transition-all main-background-container relative overflow-hidden ai-teaching-assistant">
    <!-- 增强的教育主题背景渐变层 -->
    <div class="absolute inset-0 bg-gradient-to-br from-blue-50/90 via-indigo-50/70 to-purple-50/50 dark:from-slate-900/95 dark:via-blue-900/40 dark:to-indigo-900/30"></div>

    <!-- 教育氛围装饰层 -->
    <div class="absolute inset-0 opacity-[0.02] dark:opacity-[0.03]">
      <!-- 主要装饰光晕 -->
      <div class="absolute top-8 left-8 w-40 h-40 rounded-full bg-gradient-to-br from-blue-400/60 via-indigo-500/40 to-purple-500/30 blur-3xl animate-pulse"></div>
      <div class="absolute top-1/4 right-16 w-32 h-32 rounded-full bg-gradient-to-br from-green-400/50 via-teal-500/30 to-cyan-500/20 blur-2xl"></div>
      <div class="absolute bottom-16 left-1/3 w-48 h-48 rounded-full bg-gradient-to-br from-orange-400/40 via-yellow-500/25 to-amber-500/15 blur-3xl"></div>
      <div class="absolute bottom-1/4 right-1/4 w-36 h-36 rounded-full bg-gradient-to-br from-pink-400/45 via-rose-500/30 to-red-500/20 blur-2xl"></div>
    </div>

    <!-- 教育主题几何装饰增强版 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <!-- 书本图案群 -->
      <div class="absolute top-20 right-20 w-8 h-8 opacity-[0.04] dark:opacity-[0.06] transform rotate-12">
        <svg viewBox="0 0 24 24" fill="currentColor" class="w-full h-full text-blue-600 dark:text-blue-400">
          <path d="M21 5c-1.11-.35-2.33-.5-3.5-.5-1.95 0-4.05.4-5.5 1.5-1.45-1.1-3.55-1.5-5.5-1.5S2.45 4.9 1 6v14.65c0 .25.25.5.5.5.1 0 .15-.05.25-.05C3.1 20.45 5.05 20 6.5 20c1.95 0 4.05.4 5.5 1.5 1.35-.85 3.8-1.5 5.5-1.5 1.65 0 3.35.3 4.75 1.05.1.05.15.05.25.05.25 0 .5-.25.5-.5V6c-.6-.45-1.25-.75-2-1zm0 13.5c-1.1-.35-2.3-.5-3.5-.5-1.7 0-4.15.65-5.5 1.5V8c1.35-.85 3.8-1.5 5.5-1.5 1.2 0 2.4.15 3.5.5v11.5z"/>
        </svg>
      </div>

      <!-- 灯泡图案 -->
      <div class="absolute bottom-28 left-24 w-6 h-6 opacity-[0.04] dark:opacity-[0.06] transform -rotate-6">
        <svg viewBox="0 0 24 24" fill="currentColor" class="w-full h-full text-yellow-600 dark:text-yellow-400">
          <path d="M9 21c0 .5.4 1 1 1h4c.6 0 1-.5 1-1v-1H9v1zm3-19C8.1 2 5 5.1 5 9c0 2.4 1.2 4.5 3 5.7V17c0 .5.4 1 1 1h6c.6 0 1-.5 1-1v-2.3c1.8-1.3 3-3.4 3-5.7 0-3.9-3.1-7-7-7z"/>
        </svg>
      </div>

      <!-- 铅笔图案 -->
      <div class="absolute top-1/3 left-16 w-5 h-5 opacity-[0.03] dark:opacity-[0.05] transform rotate-45">
        <svg viewBox="0 0 24 24" fill="currentColor" class="w-full h-full text-green-600 dark:text-green-400">
          <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
        </svg>
      </div>

      <!-- 星星装饰点 -->
      <div class="absolute top-1/2 left-12 w-3 h-3 opacity-[0.06] dark:opacity-[0.08]">
        <svg viewBox="0 0 24 24" fill="currentColor" class="w-full h-full text-purple-500 dark:text-purple-400">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
      </div>

      <!-- 更多几何装饰点 -->
      <div class="absolute top-1/4 right-1/3 w-2 h-2 rounded-full bg-blue-400/60 opacity-[0.1] dark:opacity-[0.15] animate-pulse"></div>
      <div class="absolute bottom-1/3 right-16 w-4 h-4 rounded-full bg-green-400/50 opacity-[0.08] dark:opacity-[0.12]"></div>
      <div class="absolute top-3/4 left-1/4 w-1 h-1 rounded-full bg-orange-400/70 opacity-[0.12] dark:opacity-[0.18]"></div>
    </div>

    <!-- 主内容区域 -->
    <div class="relative z-10 h-full overflow-hidden p-3 flex flex-col" :class="getMobileClass">
      <NLayout class="z-40 transition h-full flex-1 flex backdrop-blur-sm dual-column-layout">
        <!-- 新的双栏布局：聊天区域 + 代码预览区域 -->
        <div class="flex flex-1 h-full transition-all duration-300 gap-4">
          <!-- 聊天区域 - 增强版 -->
          <div :class="[chatAreaClass, 'transition-all duration-300 ease-in-out relative animate__animated animate__fadeIn chat-area']">
            <div :class="[chatContentClass, 'h-full bg-white/98 dark:bg-gray-800/98 rounded-xl ds-shadow-xl overflow-hidden transition-all duration-300 backdrop-blur-md border border-white/30 dark:border-gray-700/40 relative group']">
              <!-- 聊天容器装饰边框 -->
              <div class="absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 dark:from-blue-400/10 dark:via-transparent dark:to-purple-400/10 pointer-events-none"></div>

              <!-- 顶部装饰光线 -->
              <div class="absolute top-0 left-1/4 right-1/4 h-px bg-gradient-to-r from-transparent via-blue-400/30 to-transparent dark:via-blue-300/40"></div>

              <ChatBase class="w-full h-full relative z-10" />
            </div>
          </div>

          <!-- 代码与预览区域 - 增强版 -->
          <div :class="[codePreviewAreaClass, 'transition-all duration-300 ease-in-out animate__animated animate__fadeIn']" role="complementary" aria-label="代码预览区域">
            <div class="relative h-full bg-white/98 dark:bg-gray-800/98 rounded-xl ds-shadow-xl overflow-hidden backdrop-blur-md border border-white/30 dark:border-gray-700/40 group">
              <!-- 代码预览装饰边框 -->
              <div class="absolute inset-0 rounded-xl bg-gradient-to-br from-green-500/5 via-transparent to-teal-500/5 dark:from-green-400/10 dark:via-transparent dark:to-teal-400/10 pointer-events-none"></div>

              <!-- 顶部装饰光线 -->
              <div class="absolute top-0 left-1/4 right-1/4 h-px bg-gradient-to-r from-transparent via-green-400/30 to-transparent dark:via-green-300/40"></div>

              <!-- 美化的关闭按钮 -->
              <button
                v-if="previewVisible"
                @click="previewVisible = false"
                class="absolute top-4 right-4 z-30 p-2.5 rounded-full bg-white/95 dark:bg-gray-800/95 ds-shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300 hover-float transform hover:scale-110 active:scale-95 backdrop-blur-md border border-white/40 dark:border-gray-600/50 group/btn"
                title="关闭预览"
                aria-label="关闭代码预览"
                tabindex="0"
                @keydown.enter="previewVisible = false"
                @keydown.space="previewVisible = false"
                @keydown.escape="previewVisible = false"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 dark:text-gray-300 group-hover/btn:text-red-500 dark:group-hover/btn:text-red-400 transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              <div class="h-full relative z-10">
                <CodePreview
                  :code="currentCode"
                  @update:code="handleCodeUpdate"
                  @element-edited="handleElementEdited"
                />
              </div>
            </div>
          </div>

          <!-- 快速开始侧边栏 - 增强版 -->
          <div :class="[quickStartAreaClass, 'transition-all duration-300 ease-in-out animate__animated animate__fadeIn']" role="complementary" aria-label="快速开始区域">
            <div class="h-full relative">
              <!-- 侧边栏装饰背景 -->
              <div class="absolute inset-0 rounded-xl bg-gradient-to-br from-purple-500/3 via-transparent to-indigo-500/3 dark:from-purple-400/8 dark:via-transparent dark:to-indigo-400/8 pointer-events-none"></div>
              <QuickStartSidebar @use-prompt="handleUsePrompt" />
            </div>
          </div>
        </div>
      </NLayout>
    </div>
  </div>
</template>

<style scoped>
/* 美化背景 */
:deep(.n-layout) {
  background: linear-gradient(135deg, rgba(240, 249, 255, 0.8), rgba(249, 250, 251, 0.95)) !important;
}

:deep(.dark .n-layout) {
  background: linear-gradient(135deg, rgba(17, 24, 39, 0.95), rgba(31, 41, 55, 0.9)) !important;
}

/* 美化内容区域 */
:deep(.chat-container) {
  border-radius: 1rem;
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.05),
    0 8px 10px -6px rgba(0, 0, 0, 0.01),
    0 0 0 1px rgba(0, 0, 0, 0.02);
  overflow: hidden;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.dark .chat-container) {
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.2),
    0 8px 10px -6px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* 美化输入区域 */
:deep(.message-input-container) {
  backdrop-filter: blur(12px);
  border-top: 1px solid rgba(59, 130, 246, 0.1);
  box-shadow:
    0 -4px 10px -3px rgba(0, 0, 0, 0.03),
    0 -2px 8px -4px rgba(0, 0, 0, 0.02);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.dark .message-input-container) {
  border-top: 1px solid rgba(59, 130, 246, 0.08);
  box-shadow:
    0 -4px 10px -3px rgba(0, 0, 0, 0.1),
    0 -2px 8px -4px rgba(0, 0, 0, 0.08);
}

/* 美化消息气泡 */
:deep(.message-container) {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.message-container:hover) {
  transform: translateY(-2px);
}

/* 美化按钮和交互元素 */
:deep(button), :deep(.btn) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(button:hover), :deep(.btn:hover) {
  transform: translateY(-1px);
}

/* 美化滚动条 */
:deep(::-webkit-scrollbar) {
  width: 5px;
}

:deep(::-webkit-scrollbar-thumb) {
  background-color: rgba(59, 130, 246, 0.3);
  border-radius: 5px;
}

:deep(.dark ::-webkit-scrollbar-thumb) {
  background-color: rgba(59, 130, 246, 0.2);
}

:deep(::-webkit-scrollbar-track) {
  background: transparent;
}

/* 添加入场动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

:deep(.chat-container) {
  animation: fadeInUp 0.5s ease-out forwards;
}

/* 添加微妙的背景图案 */
:deep(body) {
  background-image:
    radial-gradient(circle at 25px 25px, rgba(59, 130, 246, 0.05) 2px, transparent 0),
    radial-gradient(circle at 75px 75px, rgba(59, 130, 246, 0.03) 2px, transparent 0);
  background-size: 100px 100px;
}

:deep(.dark body) {
  background-image:
    radial-gradient(circle at 25px 25px, rgba(59, 130, 246, 0.1) 2px, transparent 0),
    radial-gradient(circle at 75px 75px, rgba(59, 130, 246, 0.05) 2px, transparent 0);
  background-size: 100px 100px;
}

/* 聊天区域布局过渡效果 */
.chat-area {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 确保内容居中时的平滑过渡 */
.chat-area .max-w-6xl {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬浮按钮效果 */
.hover-float {
  transition: transform 0.3s ease;
}

.hover-float:hover {
  transform: translateY(-2px);
}

/* 双栏布局优化 */
.dual-column-layout {
  min-height: 0; /* 确保flex子元素可以正确收缩 */
}

/* 响应式优化 */
@media (max-width: 768px) {
  .chat-area {
    width: 100% !important;
  }
}

/* 确保聊天内容在单栏模式下居中显示 */
.chat-area:only-child {
  display: flex;
  justify-content: center;
}

.chat-area:only-child > div {
  max-width: 1536px; /* 6xl */
  width: 100%;
}
</style>
