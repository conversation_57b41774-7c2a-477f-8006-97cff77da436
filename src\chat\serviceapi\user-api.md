# 用户端API接口文档

## 概述

本文档详细描述了 DeepCreate 项目用户端的所有 API 接口，包括认证、用户管理、应用、订单支付、签到等功能。

## 接口分类

### 1. 认证相关接口

#### 1.1 用户注册

**接口地址**: `POST /auth/register`

**功能描述**: 用户注册账号

**请求参数**:
```typescript
interface UserRegisterRequest {
  username: string;                  // 用户名
  password: string;                  // 密码
  email?: string;                    // 邮箱（可选）
  phone?: string;                    // 手机号（可选）
  verifyCode?: string;               // 验证码（可选）
  inviteCode?: string;               // 邀请码（可选）
}
```

**请求示例**:
```json
{
  "username": "testuser",
  "password": "123456",
  "email": "<EMAIL>",
  "verifyCode": "123456"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": 123,
      "username": "testuser",
      "email": "<EMAIL>",
      "avatar": null,
      "role": "user",
      "status": 1,
      "balance": 100
    }
  }
}
```

#### 1.2 用户登录

**接口地址**: `POST /auth/login`

**功能描述**: 用户登录

**请求参数**:
```typescript
interface UserLoginRequest {
  username: string;                  // 用户名或邮箱
  password: string;                  // 密码
}
```

**请求示例**:
```json
{
  "username": "testuser",
  "password": "123456"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": 123,
      "username": "testuser",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatar.jpg",
      "role": "user",
      "status": 1,
      "balance": 95.5
    }
  }
}
```

#### 1.3 验证码登录

**接口地址**: `POST /auth/loginWithCaptcha`

**功能描述**: 使用验证码登录

**请求参数**:
```typescript
interface CaptchaLoginRequest {
  phone: string;                     // 手机号
  verifyCode: string;                // 验证码
}
```

#### 1.4 获取用户信息

**接口地址**: `GET /auth/getInfo`

**功能描述**: 获取当前登录用户信息

**请求头**:
```
Authorization: Bearer {token}
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "id": 123,
    "username": "testuser",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "role": "user",
    "status": 1,
    "balance": 95.5,
    "createdAt": "2024-01-20T10:30:00.000Z"
  }
}
```

#### 1.5 修改密码

**接口地址**: `POST /auth/updatePassword`

**功能描述**: 修改用户密码

**请求参数**:
```typescript
interface UpdatePasswordRequest {
  oldPassword: string;               // 原密码
  newPassword: string;               // 新密码
}
```

### 2. 用户管理接口

#### 2.1 更新用户信息

**接口地址**: `POST /user/update`

**功能描述**: 更新用户个人信息

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface UpdateUserRequest {
  username?: string;                 // 用户名
  email?: string;                    // 邮箱
  avatar?: string;                   // 头像URL
  nickname?: string;                 // 昵称
  bio?: string;                      // 个人简介
  school?: string;                   // 学校
  grade?: string;                    // 年级
}
```

**请求示例**:
```json
{
  "nickname": "小明",
  "bio": "我是一个小学生",
  "school": "北京小学",
  "grade": "三年级"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "更新成功",
  "data": {
    "id": 123,
    "username": "testuser",
    "nickname": "小明",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "bio": "我是一个小学生",
    "school": "北京小学",
    "grade": "三年级"
  }
}
```

### 3. 应用相关接口

#### 3.1 获取应用分类

**接口地址**: `GET /app/queryCats`

**功能描述**: 获取应用分类列表

**响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "编程助手",
      "icon": "https://example.com/icon1.png",
      "description": "编程相关的AI助手",
      "sort": 1,
      "status": 1
    },
    {
      "id": 2,
      "name": "学习助手",
      "icon": "https://example.com/icon2.png",
      "description": "学习相关的AI助手",
      "sort": 2,
      "status": 1
    }
  ]
}
```

#### 3.2 获取应用列表

**接口地址**: `GET /app/list`

**功能描述**: 获取应用列表

**请求参数**:
```typescript
interface QueryAppRequest {
  catId?: number;                    // 分类ID
  page?: number;                     // 页码，默认1
  size?: number;                     // 每页数量，默认20
  keyword?: string;                  // 搜索关键词
  status?: number;                   // 状态筛选
}
```

**请求示例**:
```
GET /app/list?catId=1&page=1&size=10&keyword=编程
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "id": 10,
        "name": "编程助手",
        "description": "专业的编程辅助工具",
        "avatar": "https://example.com/app-avatar.png",
        "catId": 1,
        "preset": "你是一个专业的编程助手...",
        "demoData": [
          "帮我写一个Python函数",
          "解释这段代码的作用"
        ],
        "status": 1,
        "isCollected": false,
        "usageCount": 1250
      }
    ],
    "count": 50,
    "page": 1,
    "size": 10
  }
}
```

#### 3.3 搜索应用

**接口地址**: `POST /app/searchList`

**功能描述**: 搜索应用

**请求参数**:
```typescript
interface SearchAppRequest {
  keyword: string;                   // 搜索关键词
  page?: number;                     // 页码
  size?: number;                     // 每页数量
}
```

#### 3.4 收藏/取消收藏应用

**接口地址**: `POST /app/collect`

**功能描述**: 收藏或取消收藏应用

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface CollectAppRequest {
  appId: number;                     // 应用ID
  status: boolean;                   // true-收藏，false-取消收藏
}
```

**请求示例**:
```json
{
  "appId": 10,
  "status": true
}
```

### 4. 套餐和订单接口

#### 4.1 获取套餐列表

**接口地址**: `GET /crami/queryAllPackage`

**功能描述**: 获取所有可用套餐

**请求参数**:
```typescript
interface QueryPackageRequest {
  status?: number;                   // 状态筛选，1-启用
}
```

**响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "基础套餐",
      "description": "适合轻度使用",
      "price": 9.9,
      "originalPrice": 19.9,
      "model3Count": 100,
      "model4Count": 10,
      "drawMjCount": 5,
      "validityPeriod": 30,
      "status": 1,
      "sort": 1,
      "isRecommend": false
    }
  ]
}
```

#### 4.2 购买套餐

**接口地址**: `POST /order/buy`

**功能描述**: 购买套餐创建订单

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface BuyRequest {
  goodsId: number;                   // 套餐ID
  payType?: string;                  // 支付方式：wxpay, alipay等
}
```

**请求示例**:
```json
{
  "goodsId": 1,
  "payType": "wxpay"
}
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "orderId": "ORDER_20240120_123456",
    "payUrl": "https://pay.example.com/...",
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "amount": 9.9
  }
}
```

#### 4.3 查询订单状态

**接口地址**: `GET /order/queryByOrderId`

**功能描述**: 查询订单支付状态

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface QueryOrderRequest {
  orderId: string;                   // 订单ID
}
```

**请求示例**:
```
GET /order/queryByOrderId?orderId=ORDER_20240120_123456
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "orderId": "ORDER_20240120_123456",
    "status": 1,
    "statusText": "已支付",
    "amount": 9.9,
    "goodsName": "基础套餐",
    "payTime": "2024-01-20T10:30:00.000Z",
    "createTime": "2024-01-20T10:25:00.000Z"
  }
}
```

### 5. 签到相关接口

#### 5.1 用户签到

**接口地址**: `POST /signin/sign`

**功能描述**: 用户每日签到

**请求头**:
```
Authorization: Bearer {token}
```

**响应格式**:
```json
{
  "success": true,
  "message": "签到成功",
  "data": {
    "reward": 5,
    "continuousDays": 3,
    "totalDays": 15,
    "todayReward": 5
  }
}
```

#### 5.2 获取签到记录

**接口地址**: `GET /signin/signinLog`

**功能描述**: 获取用户签到记录

**请求头**:
```
Authorization: Bearer {token}
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "continuousDays": 3,
    "totalDays": 15,
    "todaySigned": true,
    "monthlyRecord": [
      {
        "date": "2024-01-20",
        "signed": true,
        "reward": 5
      },
      {
        "date": "2024-01-19",
        "signed": true,
        "reward": 5
      }
    ]
  }
}
```

### 6. 文件上传接口

#### 6.1 上传文件

**接口地址**: `POST /upload/file`

**功能描述**: 上传文件

**请求头**:
```
Content-Type: multipart/form-data
```

**请求参数**:
```typescript
interface UploadFileRequest {
  file: File;                        // 文件对象
  dir?: string;                      // 目录名称，默认'ai'
}
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "url": "https://example.com/uploads/ai/20240120/file.jpg",
    "filename": "file.jpg",
    "size": 102400,
    "type": "image/jpeg"
  }
}
```

#### 6.2 从URL上传文件

**接口地址**: `POST /upload/fileFromUrl`

**功能描述**: 从URL地址上传文件

**请求参数**:
```typescript
interface UploadFromUrlRequest {
  url: string;                       // 文件URL
  dir?: string;                      // 目录名称，默认'ai'
}
```

### 7. 插件相关接口

#### 7.1 获取插件列表

**接口地址**: `GET /plugin/pluginList`

**功能描述**: 获取可用插件列表

**响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "网络搜索",
      "description": "实时搜索网络信息",
      "icon": "https://example.com/plugin-icon.png",
      "status": 1,
      "parameters": {
        "searchEngine": "google",
        "maxResults": 10
      }
    }
  ]
}
```

### 8. 全局配置接口

#### 8.1 获取前端配置

**接口地址**: `GET /config/queryFronet`

**功能描述**: 获取前端可访问的配置信息

**响应格式**:
```json
{
  "success": true,
  "data": {
    "siteName": "DeepCreate",
    "robotAvatar": "https://example.com/robot-avatar.png",
    "siteRobotName": "小狐狸",
    "clientLogoPath": "https://example.com/logo.png",
    "isUseWxLogin": true,
    "payEpayStatus": true,
    "payWechatStatus": true,
    "registerSendStatus": true,
    "registerSendModel3Count": 100,
    "registerSendModel4Count": 10
  }
}
```

## 错误处理

### 常见错误码

- `401` - 未授权，需要登录
- `403` - 权限不足
- `400` - 请求参数错误
- `404` - 资源不存在
- `429` - 请求频率过高
- `500` - 服务器内部错误

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "data": null
}
```

## 前端实现示例

### 1. API客户端封装

```typescript
import { get, post } from '@/utils/request';

// 认证相关
export const authAPI = {
  register: (data: UserRegisterRequest) => post({ url: '/auth/register', data }),
  login: (data: UserLoginRequest) => post({ url: '/auth/login', data }),
  getInfo: () => get({ url: '/auth/getInfo' }),
  updatePassword: (data: UpdatePasswordRequest) => post({ url: '/auth/updatePassword', data })
};

// 用户相关
export const userAPI = {
  update: (data: UpdateUserRequest) => post({ url: '/user/update', data })
};

// 应用相关
export const appAPI = {
  getCategories: () => get({ url: '/app/queryCats' }),
  getList: (params: QueryAppRequest) => get({ url: '/app/list', params }),
  search: (data: SearchAppRequest) => post({ url: '/app/searchList', data }),
  collect: (data: CollectAppRequest) => post({ url: '/app/collect', data })
};

// 订单相关
export const orderAPI = {
  getPackages: (params?: QueryPackageRequest) => get({ url: '/crami/queryAllPackage', params }),
  buy: (data: BuyRequest) => post({ url: '/order/buy', data }),
  queryStatus: (params: QueryOrderRequest) => get({ url: '/order/queryByOrderId', params })
};

// 签到相关
export const signinAPI = {
  sign: () => post({ url: '/signin/sign', data: {} }),
  getLog: () => get({ url: '/signin/signinLog' })
};
```

### 2. 状态管理

```typescript
// 用户状态管理
export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null as UserInfo | null,
    token: localStorage.getItem('token') || '',
    isLoggedIn: false
  }),

  actions: {
    async login(loginData: UserLoginRequest) {
      const response = await authAPI.login(loginData);
      this.token = response.data.token;
      this.userInfo = response.data.userInfo;
      this.isLoggedIn = true;
      localStorage.setItem('token', this.token);
    },

    async getUserInfo() {
      if (!this.token) return;
      
      try {
        const response = await authAPI.getInfo();
        this.userInfo = response.data;
        this.isLoggedIn = true;
      } catch (error) {
        this.logout();
      }
    },

    logout() {
      this.token = '';
      this.userInfo = null;
      this.isLoggedIn = false;
      localStorage.removeItem('token');
    }
  }
});
```
