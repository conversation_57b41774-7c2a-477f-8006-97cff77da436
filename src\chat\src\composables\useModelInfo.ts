/**
 * 模型信息管理 Composable
 */
import { computed } from 'vue';
import { useChatStore } from '@/store';
import { parseConfigObject, getModelInfo, getFileInfo } from '@/utils/chatConfig';

export function useModelInfo() {
  const chatStore = useChatStore();

  // 获取当前活动的对话组信息
  const activeGroupInfo = computed(() => chatStore.getChatByGroupInfo());
  
  // 解析配置对象
  const configObj = computed(() => parseConfigObject(activeGroupInfo.value?.config));
  
  // 获取模型信息
  const modelInfo = computed(() => getModelInfo(configObj.value));
  
  // 获取文件信息
  const fileInfo = computed(() => getFileInfo(configObj.value));
  
  // 获取当前使用的插件
  const usingPlugin = computed(() => chatStore.currentPlugin);
  
  // 获取当前应用ID
  const activeAppId = computed(() => activeGroupInfo.value?.appId || 0);
  
  // 模型相关的计算属性
  const activeModel = computed(() => {
    if (usingPlugin.value?.deductType && usingPlugin.value?.deductType !== 0) {
      return usingPlugin.value?.parameters || '';
    }
    return modelInfo.value.model;
  });
  
  const activeModelName = computed(() => {
    return usingPlugin.value?.pluginName || modelInfo.value.modelName || 'AI';
  });
  
  const activeModelAvatar = computed(() => {
    return usingPlugin.value?.pluginImg || modelInfo.value.modelAvatar || '';
  });
  
  const activeModelKeyType = computed(() => {
    return modelInfo.value.keyType;
  });
  
  const activeModelFileUpload = computed(() => {
    return modelInfo.value.isFileUpload;
  });
  
  // 文件相关的计算属性
  const fileParsing = computed(() => fileInfo.value.fileParsing);
  const fileName = computed(() => fileInfo.value.fileName);
  
  // 模型类型检查
  const isFilesModel = computed(() => [1, 2, 3].includes(activeModelFileUpload.value));
  
  const notSwitchModel = computed(() => {
    return (
      (activeAppId.value && 
        (modelInfo.value.isFixedModel === 1 || modelInfo.value.isGPTs === 1)) ||
      (usingPlugin.value && usingPlugin.value?.deductType !== 0)
    );
  });

  return {
    // 基础信息
    activeGroupInfo,
    configObj,
    modelInfo,
    fileInfo,
    usingPlugin,
    activeAppId,
    
    // 模型相关
    activeModel,
    activeModelName,
    activeModelAvatar,
    activeModelKeyType,
    activeModelFileUpload,
    
    // 文件相关
    fileParsing,
    fileName,
    
    // 状态检查
    isFilesModel,
    notSwitchModel
  };
}
