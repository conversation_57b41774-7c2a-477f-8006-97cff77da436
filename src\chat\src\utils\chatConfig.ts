/**
 * 聊天配置相关工具函数
 */

/**
 * 解析配置对象字符串
 * @param configString 配置字符串
 * @returns 解析后的配置对象
 */
export function parseConfigObject(configString?: string): any {
  if (!configString) {
    return {};
  }

  try {
    return JSON.parse(configString);
  } catch (e) {
    console.warn('配置对象解析失败:', e);
    return {};
  }
}

/**
 * 获取模型信息
 * @param configObj 配置对象
 * @returns 模型信息
 */
export function getModelInfo(configObj: any) {
  return {
    model: String(configObj?.modelInfo?.model ?? ''),
    modelName: String(configObj?.modelInfo?.modelName ?? ''),
    modelAvatar: String(configObj?.modelInfo?.modelAvatar ?? ''),
    keyType: Number(configObj?.modelInfo?.keyType ?? 1),
    isFileUpload: Number(configObj?.modelInfo?.isFileUpload ?? 0),
    isFixedModel: Number(configObj?.modelInfo?.isFixedModel ?? 0),
    isGPTs: Number(configObj?.modelInfo?.isGPTs ?? 0)
  };
}

/**
 * 获取文件信息
 * @param configObj 配置对象
 * @returns 文件信息
 */
export function getFileInfo(configObj: any) {
  return {
    fileParsing: String(configObj?.fileInfo?.fileParsing ?? ''),
    fileName: String(configObj?.fileInfo?.fileName ?? '')
  };
}

/**
 * 检查是否为特定模型类型
 * @param model 模型名称
 * @param targetModel 目标模型名称
 * @returns 是否匹配
 */
export function isModelType(model: string, targetModel: string): boolean {
  return model === targetModel || model.includes(targetModel);
}

/**
 * 检查是否为图像生成模型
 * @param model 模型名称
 * @returns 是否为图像生成模型
 */
export function isImageModel(model: string): boolean {
  const imageModels = ['dall-e-3', 'midjourney', 'stable-diffusion', 'gpt-image-1'];
  return imageModels.some(imageModel => isModelType(model, imageModel)) || model.includes('flux');
}

/**
 * 检查是否为视频生成模型
 * @param model 模型名称
 * @returns 是否为视频生成模型
 */
export function isVideoModel(model: string): boolean {
  const videoModels = ['luma-video', 'cog-video'];
  return videoModels.some(videoModel => isModelType(model, videoModel));
}

/**
 * 检查是否为音频生成模型
 * @param model 模型名称
 * @returns 是否为音频生成模型
 */
export function isAudioModel(model: string): boolean {
  return isModelType(model, 'suno-music');
}

/**
 * 检查是否为PPT生成模型
 * @param model 模型名称
 * @returns 是否为PPT生成模型
 */
export function isPPTModel(model: string): boolean {
  return isModelType(model, 'ai-ppt');
}
