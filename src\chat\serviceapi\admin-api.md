# 管理端API接口文档

## 概述

本文档详细描述了 DeepCreate 项目管理端的所有 API 接口，包括用户管理、应用管理、模型管理、订单管理、统计分析等功能。

## 权限说明

- **AdminAuthGuard**: 管理员权限，可访问大部分管理功能
- **SuperAuthGuard**: 超级管理员权限，可访问所有管理功能

## 接口分类

### 1. 用户管理

#### 1.1 查询所有用户

**接口地址**: `GET /user/queryAll`

**功能描述**: 查询所有用户列表

**权限要求**: AdminAuthGuard

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```typescript
interface QueryAllUserRequest {
  page?: number;                     // 页码，默认1
  size?: number;                     // 每页数量，默认20
  keyword?: string;                  // 搜索关键词（用户名、邮箱）
  status?: number;                   // 状态筛选：1-正常，0-禁用
  role?: string;                     // 角色筛选：user, admin, super
  startDate?: string;                // 注册开始日期
  endDate?: string;                  // 注册结束日期
}
```

**请求示例**:
```
GET /user/queryAll?page=1&size=20&keyword=test&status=1
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "id": 123,
        "username": "testuser",
        "email": "<EMAIL>",
        "avatar": "https://example.com/avatar.jpg",
        "role": "user",
        "status": 1,
        "balance": 95.5,
        "totalRecharge": 100.0,
        "totalConsumption": 4.5,
        "registerIp": "***********",
        "lastLoginTime": "2024-01-20T10:30:00.000Z",
        "createdAt": "2024-01-15T08:00:00.000Z"
      }
    ],
    "count": 1250,
    "page": 1,
    "size": 20
  }
}
```

#### 1.2 用户充值

**接口地址**: `POST /user/recharge`

**功能描述**: 管理员为用户充值

**权限要求**: SuperAuthGuard

**请求参数**:
```typescript
interface UserRechargeRequest {
  userId: number;                    // 用户ID
  amount: number;                    // 充值金额
  reason?: string;                   // 充值原因
}
```

**请求示例**:
```json
{
  "userId": 123,
  "amount": 50.0,
  "reason": "活动奖励"
}
```

#### 1.3 更新用户状态

**接口地址**: `POST /user/updateStatus`

**功能描述**: 更新用户状态（启用/禁用）

**权限要求**: AdminAuthGuard

**请求参数**:
```typescript
interface UpdateUserStatusRequest {
  userId: number;                    // 用户ID
  status: number;                    // 状态：1-启用，0-禁用
  reason?: string;                   // 操作原因
}
```

### 2. 应用管理

#### 2.1 查询应用列表

**接口地址**: `GET /app/queryApp`

**功能描述**: 查询所有应用列表

**权限要求**: AdminAuthGuard

**请求参数**:
```typescript
interface QueryAppRequest {
  page?: number;                     // 页码
  size?: number;                     // 每页数量
  catId?: number;                    // 分类ID
  status?: number;                   // 状态筛选
  keyword?: string;                  // 搜索关键词
}
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "id": 10,
        "name": "编程助手",
        "description": "专业的编程辅助工具",
        "avatar": "https://example.com/app-avatar.png",
        "catId": 1,
        "catName": "编程工具",
        "preset": "你是一个专业的编程助手...",
        "demoData": ["帮我写一个Python函数", "解释这段代码的作用"],
        "status": 1,
        "sort": 1,
        "isRecommend": true,
        "usageCount": 1250,
        "createdAt": "2024-01-10T10:00:00.000Z"
      }
    ],
    "count": 50,
    "page": 1,
    "size": 20
  }
}
```

#### 2.2 创建应用

**接口地址**: `POST /app/createApp`

**功能描述**: 创建新应用

**权限要求**: SuperAuthGuard

**请求参数**:
```typescript
interface CreateAppRequest {
  name: string;                      // 应用名称
  description: string;               // 应用描述
  avatar?: string;                   // 应用头像
  catId: number;                     // 分类ID
  preset: string;                    // 预设提示词
  demoData?: string[];               // 示例数据
  status?: number;                   // 状态，默认1
  sort?: number;                     // 排序
  isRecommend?: boolean;             // 是否推荐
}
```

#### 2.3 更新应用

**接口地址**: `POST /app/updateApp`

**功能描述**: 更新应用信息

**权限要求**: SuperAuthGuard

**请求参数**: 与创建应用相同，需额外包含 `id` 字段

#### 2.4 删除应用

**接口地址**: `POST /app/delApp`

**功能描述**: 删除应用

**权限要求**: SuperAuthGuard

**请求参数**:
```typescript
interface DeleteAppRequest {
  id: number;                        // 应用ID
}
```

#### 2.5 批量导入应用

**接口地址**: `POST /app/batchImportApps`

**功能描述**: 批量导入应用

**权限要求**: SuperAuthGuard

**请求参数**:
```typescript
interface BatchImportAppsRequest {
  jsonContent: string;               // JSON格式的应用数据
  validateOnly: boolean;             // 是否仅验证不导入
}
```

### 3. 应用分类管理

#### 3.1 查询分类列表

**接口地址**: `GET /app/queryAppCats`

**功能描述**: 查询应用分类列表

**权限要求**: AdminAuthGuard

**请求参数**:
```typescript
interface QueryCatsRequest {
  page?: number;                     // 页码
  size?: number;                     // 每页数量
  status?: number;                   // 状态筛选
  name?: string;                     // 名称搜索
}
```

#### 3.2 创建分类

**接口地址**: `POST /app/createAppCats`

**功能描述**: 创建应用分类

**权限要求**: SuperAuthGuard

**请求参数**:
```typescript
interface CreateCatsRequest {
  name: string;                      // 分类名称
  icon?: string;                     // 分类图标
  description?: string;              // 分类描述
  sort?: number;                     // 排序
  status?: number;                   // 状态
}
```

#### 3.3 更新分类

**接口地址**: `POST /app/updateAppCats`

**功能描述**: 更新应用分类

**权限要求**: SuperAuthGuard

#### 3.4 删除分类

**接口地址**: `POST /app/delAppCats`

**功能描述**: 删除应用分类

**权限要求**: SuperAuthGuard

### 4. 模型管理

#### 4.1 查询模型列表

**接口地址**: `GET /models/query`

**功能描述**: 查询AI模型列表

**权限要求**: AdminAuthGuard

**请求参数**:
```typescript
interface QueryModelRequest {
  page?: number;                     // 页码
  size?: number;                     // 每页数量
  status?: number;                   // 状态筛选
  type?: number;                     // 模型类型筛选
  keyword?: string;                  // 搜索关键词
}
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "id": 1,
        "model": "gpt-3.5-turbo",
        "modelName": "GPT-3.5",
        "modelType": 1,
        "modelAvatar": "https://example.com/gpt-avatar.png",
        "deductType": 1,
        "deduct": 1,
        "status": 1,
        "maxTokens": 4096,
        "maxResponseTokens": 2048,
        "temperature": 0.7,
        "isFileUpload": false,
        "isSystemPrompt": true,
        "keyType": 1,
        "proxyUrl": "https://api.openai.com",
        "timeout": 30000,
        "createdAt": "2024-01-10T10:00:00.000Z"
      }
    ],
    "count": 15,
    "page": 1,
    "size": 20
  }
}
```

#### 4.2 设置模型

**接口地址**: `POST /models/setModel`

**功能描述**: 创建或更新模型配置

**权限要求**: SuperAuthGuard

**请求参数**:
```typescript
interface SetModelRequest {
  id?: number;                       // 模型ID（更新时需要）
  model: string;                     // 模型标识
  modelName: string;                 // 模型显示名称
  modelType: number;                 // 模型类型：1-文本，2-图像，3-音频
  modelAvatar?: string;              // 模型头像
  deductType: number;                // 扣费类型：1-按次，2-按Token
  deduct: number;                    // 扣费数量
  status: number;                    // 状态：1-启用，0-禁用
  maxTokens?: number;                // 最大Token数
  maxResponseTokens?: number;        // 最大响应Token数
  temperature?: number;              // 温度参数
  isFileUpload?: boolean;            // 是否支持文件上传
  isSystemPrompt?: boolean;          // 是否支持系统提示词
  keyType?: number;                  // 密钥类型
  proxyUrl?: string;                 // 代理URL
  timeout?: number;                  // 超时时间
}
```

#### 4.3 删除模型

**接口地址**: `POST /models/delModel`

**功能描述**: 删除模型配置

**权限要求**: SuperAuthGuard

**请求参数**:
```typescript
interface DeleteModelRequest {
  id: number;                        // 模型ID
}
```

### 5. 套餐管理

#### 5.1 查询套餐列表

**接口地址**: `GET /crami/queryAllPackage`

**功能描述**: 查询所有套餐（管理端）

**权限要求**: AdminAuthGuard

**请求参数**:
```typescript
interface QueryAllPackageRequest {
  page?: number;                     // 页码
  size?: number;                     // 每页数量
  status?: number;                   // 状态筛选
  keyword?: string;                  // 搜索关键词
}
```

#### 5.2 创建套餐

**接口地址**: `POST /crami/createPackage`

**功能描述**: 创建新套餐

**权限要求**: SuperAuthGuard

**请求参数**:
```typescript
interface CreatePackageRequest {
  name: string;                      // 套餐名称
  description?: string;              // 套餐描述
  price: number;                     // 价格
  originalPrice?: number;            // 原价
  model3Count?: number;              // GPT-3.5次数
  model4Count?: number;              // GPT-4次数
  drawMjCount?: number;              // 绘图次数
  validityPeriod?: number;           // 有效期（天）
  status?: number;                   // 状态
  sort?: number;                     // 排序
  isRecommend?: boolean;             // 是否推荐
}
```

#### 5.3 更新套餐

**接口地址**: `POST /crami/updatePackage`

**功能描述**: 更新套餐信息

**权限要求**: SuperAuthGuard

#### 5.4 删除套餐

**接口地址**: `POST /crami/deletePackage`

**功能描述**: 删除套餐

**权限要求**: SuperAuthGuard

### 6. 订单管理

#### 6.1 查询所有订单

**接口地址**: `GET /order/queryAll`

**功能描述**: 查询所有订单

**权限要求**: AdminAuthGuard

**请求参数**:
```typescript
interface QueryAllOrderRequest {
  page?: number;                     // 页码
  size?: number;                     // 每页数量
  status?: number;                   // 订单状态筛选
  payType?: string;                  // 支付方式筛选
  startDate?: string;                // 开始日期
  endDate?: string;                  // 结束日期
  keyword?: string;                  // 搜索关键词（订单号、用户名）
}
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "id": 1,
        "orderId": "ORDER_20240120_123456",
        "userId": 123,
        "username": "testuser",
        "goodsId": 1,
        "goodsName": "基础套餐",
        "price": 9.9,
        "payType": "wxpay",
        "status": 1,
        "statusText": "已支付",
        "payTime": "2024-01-20T10:30:00.000Z",
        "createdAt": "2024-01-20T10:25:00.000Z"
      }
    ],
    "count": 500,
    "page": 1,
    "size": 20
  }
}
```

#### 6.2 删除订单

**接口地址**: `POST /order/delete`

**功能描述**: 删除指定订单

**权限要求**: SuperAuthGuard

**请求参数**:
```typescript
interface DeleteOrderRequest {
  orderId: string;                   // 订单ID
}
```

#### 6.3 删除未支付订单

**接口地址**: `POST /order/deleteNotPay`

**功能描述**: 批量删除未支付订单

**权限要求**: SuperAuthGuard

### 7. 统计分析

#### 7.1 获取基础统计

**接口地址**: `GET /statistic/base`

**功能描述**: 获取基础统计数据

**权限要求**: AdminAuthGuard

**响应格式**:
```json
{
  "success": true,
  "data": {
    "userCount": 1250,
    "todayNewUsers": 15,
    "orderCount": 500,
    "todayOrders": 8,
    "totalRevenue": 12500.50,
    "todayRevenue": 89.90,
    "chatCount": 25000,
    "todayChats": 350,
    "drawCount": 5000,
    "todayDraws": 45
  }
}
```

#### 7.2 获取聊天统计

**接口地址**: `GET /statistic/chatStatistic`

**功能描述**: 获取聊天和绘画统计数据

**权限要求**: AdminAuthGuard

**请求参数**:
```typescript
interface QueryStatisticRequest {
  days: number;                      // 统计天数：7, 30, 90等
}
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "chatData": [
      {
        "date": "2024-01-20",
        "count": 350,
        "users": 120
      }
    ],
    "drawData": [
      {
        "date": "2024-01-20",
        "count": 45,
        "users": 25
      }
    ],
    "modelUsage": [
      {
        "model": "gpt-3.5-turbo",
        "count": 200,
        "percentage": 57.1
      },
      {
        "model": "gpt-4",
        "count": 150,
        "percentage": 42.9
      }
    ]
  }
}
```

#### 7.3 获取百度统计

**接口地址**: `GET /statistic/baiduVisit`

**功能描述**: 获取百度统计访问数据

**权限要求**: AdminAuthGuard

### 8. 全局配置管理

#### 8.1 查询所有配置

**接口地址**: `GET /config/queryAll`

**功能描述**: 查询所有系统配置

**权限要求**: AdminAuthGuard

**响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "configKey": "siteName",
      "configVal": "DeepCreate",
      "description": "网站名称",
      "public": 1,
      "createdAt": "2024-01-10T10:00:00.000Z"
    }
  ]
}
```

#### 8.2 设置配置

**接口地址**: `POST /config/setConfig`

**功能描述**: 设置系统配置

**权限要求**: SuperAuthGuard

**请求参数**:
```typescript
interface SetConfigRequest {
  settings: Array<{
    configKey: string;               // 配置键
    configVal: string;               // 配置值
    description?: string;            // 配置描述
    public?: number;                 // 是否公开：1-是，0-否
  }>;
}
```

### 9. 绘本管理

#### 9.1 获取所有绘本

**接口地址**: `GET /admin/storybook`

**功能描述**: 获取所有用户的绘本作品

**权限要求**: AdminAuthGuard

**请求参数**:
```typescript
interface AdminQueryStorybookRequest {
  page?: number;                     // 页码
  size?: number;                     // 每页数量
  status?: number;                   // 状态筛选
  userId?: number;                   // 用户ID筛选
  keyword?: string;                  // 搜索关键词
  startDate?: string;                // 开始日期
  endDate?: string;                  // 结束日期
}
```

#### 9.2 更新绘本状态

**接口地址**: `PUT /admin/storybook/{id}/status`

**功能描述**: 更新绘本审核状态

**权限要求**: AdminAuthGuard

**请求参数**:
```typescript
interface UpdateStorybookStatusRequest {
  status: number;                    // 状态：1-通过，2-审核中，3-拒绝
  reason?: string;                   // 审核意见
}
```

#### 9.3 设置绘本推荐

**接口地址**: `PUT /admin/storybook/{id}/recommend`

**功能描述**: 设置绘本推荐状态

**权限要求**: AdminAuthGuard

**请求参数**:
```typescript
interface SetStorybookRecommendRequest {
  isRecommended: number;             // 是否推荐：1-是，0-否
}
```

#### 9.4 获取绘本统计

**接口地址**: `GET /admin/storybook/statistics`

**功能描述**: 获取绘本相关统计数据

**权限要求**: AdminAuthGuard

## 前端实现示例

### 1. API客户端封装

```typescript
import { get, post, put, del } from '@/utils/request';

export const adminAPI = {
  // 用户管理
  user: {
    queryAll: (params: QueryAllUserRequest) => 
      get({ url: '/user/queryAll', params }),
    recharge: (data: UserRechargeRequest) => 
      post({ url: '/user/recharge', data }),
    updateStatus: (data: UpdateUserStatusRequest) => 
      post({ url: '/user/updateStatus', data })
  },

  // 应用管理
  app: {
    queryAll: (params: QueryAppRequest) => 
      get({ url: '/app/queryApp', params }),
    create: (data: CreateAppRequest) => 
      post({ url: '/app/createApp', data }),
    update: (data: CreateAppRequest & { id: number }) => 
      post({ url: '/app/updateApp', data }),
    delete: (data: DeleteAppRequest) => 
      post({ url: '/app/delApp', data }),
    batchImport: (data: BatchImportAppsRequest) => 
      post({ url: '/app/batchImportApps', data })
  },

  // 模型管理
  model: {
    query: (params: QueryModelRequest) => 
      get({ url: '/models/query', params }),
    set: (data: SetModelRequest) => 
      post({ url: '/models/setModel', data }),
    delete: (data: DeleteModelRequest) => 
      post({ url: '/models/delModel', data })
  },

  // 统计分析
  statistic: {
    getBase: () => 
      get({ url: '/statistic/base' }),
    getChatStatistic: (params: QueryStatisticRequest) => 
      get({ url: '/statistic/chatStatistic', params }),
    getBaiduVisit: (params: QueryStatisticRequest) => 
      get({ url: '/statistic/baiduVisit', params })
  },

  // 配置管理
  config: {
    queryAll: () => 
      get({ url: '/config/queryAll' }),
    setConfig: (data: SetConfigRequest) => 
      post({ url: '/config/setConfig', data })
  }
};
```

### 2. 权限控制

```typescript
// 权限检查工具
export function hasPermission(permission: string): boolean {
  const userStore = useUserStore();
  const userRole = userStore.userInfo?.role;
  
  const permissions = {
    'admin': ['user:view', 'app:view', 'order:view', 'statistic:view'],
    'super': ['*'] // 超级管理员拥有所有权限
  };
  
  if (userRole === 'super') return true;
  if (userRole === 'admin') {
    return permissions.admin.includes(permission);
  }
  
  return false;
}

// 路由守卫
router.beforeEach((to, from, next) => {
  const requiredPermission = to.meta.permission;
  
  if (requiredPermission && !hasPermission(requiredPermission)) {
    next('/403'); // 跳转到无权限页面
  } else {
    next();
  }
});
```
